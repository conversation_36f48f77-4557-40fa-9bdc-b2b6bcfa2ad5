### model
model_name_or_path: /share/model/Qwen2-VL-2B-Instruct
trust_remote_code: true

### method
stage: sft
do_train: true
finetuning_type: lora
lora_target: all

### dataset
dataset: topo2text_v1,topo2text_v2
template: qwen2_vl
cutoff_len: 2048
max_samples: 100000
preprocessing_num_workers: 16

### output
output_dir: /share/topo_lab_res/Qwen2-VL-2B-Instruct/lora
logging_steps: 5
save_steps: 100
plot_loss: true

### train
per_device_train_batch_size: 2
gradient_accumulation_steps: 8
learning_rate: 5e-05
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_steps: 0
bf16: true
ddp_timeout: 180000000
max_grad_norm: 1.0
optim: adamw_torch
packing: False
report_to: none
flash_attn: auto
lora_rank: 8
lora_alpha: 16
lora_dropout: 0
