name: topo_backup
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=5.1
  - bzip2=1.0.8
  - ca-certificates=2024.11.26
  - ld_impl_linux-64=2.40
  - libffi=3.4.4
  - libgcc-ng=11.2.0
  - libgomp=11.2.0
  - libstdcxx-ng=11.2.0
  - libuuid=1.41.5
  - ncurses=6.4
  - openssl=3.0.15
  - pip=24.2
  - python=3.11.11
  - readline=8.2
  - setuptools=75.1.0
  - sqlite=3.45.3
  - tk=8.6.14
  - wheel=0.44.0
  - xz=5.4.6
  - zlib=1.2.13
  - pip:
      - accelerate==1.0.1
      - aiofiles==23.2.1
      - aiohappyeyeballs==2.4.4
      - aiohttp==3.11.11
      - aiosignal==1.3.2
      - annotated-types==0.7.0
      - anyio==4.7.0
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - asttokens==3.0.0
      - async-lru==2.0.4
      - attrs==24.3.0
      - av==14.0.1
      - babel==2.16.0
      - beautifulsoup4==4.12.3
      - bleach==6.2.0
      - certifi==2024.12.14
      - cffi==1.17.1
      - charset-normalizer==3.4.0
      - click==8.1.8
      - comm==0.2.2
      - conda-pack==0.8.1
      - contourpy==1.3.1
      - cycler==0.12.1
      - datasets==3.1.0
      - debugpy==1.8.11
      - decorator==5.1.1
      - defusedxml==0.7.1
      - dill==0.3.8
      - docstring-parser==0.16
      - einops==0.8.0
      - executing==2.1.0
      - fastapi==0.115.6
      - fastjsonschema==2.21.1
      - ffmpy==0.5.0
      - filelock==3.16.1
      - fire==0.7.0
      - fonttools==4.55.3
      - fqdn==1.5.1
      - frozenlist==1.5.0
      - fsspec==2024.9.0
      - gradio==4.44.1
      - gradio-client==1.3.0
      - h11==0.14.0
      - httpcore==1.0.7
      - httpx==0.28.1
      - huggingface-hub==0.27.0
      - idna==3.10
      - importlib-resources==6.4.5
      - ipykernel==6.29.5
      - ipython==8.31.0
      - isoduration==20.11.0
      - jedi==0.19.2
      - jieba==0.42.1
      - jinja2==3.1.5
      - joblib==1.4.2
      - json5==0.10.0
      - jsonpointer==3.0.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2024.10.1
      - jupyter-client==8.6.3
      - jupyter-core==5.7.2
      - jupyter-events==0.11.0
      - jupyter-lsp==2.2.5
      - jupyter-server==2.15.0
      - jupyter-server-terminals==0.5.3
      - jupyterlab==4.3.4
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.27.3
      - kiwisolver==1.4.7
      - llamafactory==0.9.1
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - matplotlib==3.10.0
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - mistune==3.0.2
      - mpmath==1.3.0
      - multidict==6.1.0
      - multiprocess==0.70.16
      - nbclient==0.10.2
      - nbconvert==7.16.4
      - nbformat==5.10.4
      - nest-asyncio==1.6.0
      - networkx==3.4.2
      - nltk==3.9.1
      - notebook==7.3.2
      - notebook-shim==0.2.4
      - numpy==1.26.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.4.127
      - nvidia-cuda-nvrtc-cu12==12.4.127
      - nvidia-cuda-runtime-cu12==12.4.127
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==**********
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.4.127
      - orjson==3.10.12
      - overrides==7.7.0
      - packaging==24.2
      - pandas==2.2.3
      - pandocfilters==1.5.1
      - parso==0.8.4
      - peft==0.12.0
      - pexpect==4.9.0
      - pillow==10.4.0
      - platformdirs==4.3.6
      - prometheus-client==0.21.1
      - prompt-toolkit==3.0.48
      - propcache==0.2.1
      - protobuf==5.29.2
      - psutil==6.1.1
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - pyarrow==18.1.0
      - pycparser==2.22
      - pydantic==2.10.4
      - pydantic-core==2.27.2
      - pydub==0.25.1
      - pygments==2.18.0
      - pyparsing==3.2.0
      - python-dateutil==2.9.0.post0
      - python-json-logger==3.2.1
      - python-multipart==0.0.20
      - pytz==2024.2
      - pyyaml==6.0.2
      - pyzmq==26.2.0
      - referencing==0.35.1
      - regex==2024.11.6
      - requests==2.32.3
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - rich==13.9.4
      - rouge-chinese==1.0.3
      - rpds-py==0.22.3
      - ruff==0.8.4
      - safetensors==0.4.5
      - scipy==1.14.1
      - semantic-version==2.10.0
      - send2trash==1.8.3
      - sentencepiece==0.2.0
      - shellingham==1.5.4
      - shtab==1.7.1
      - six==1.17.0
      - sniffio==1.3.1
      - soupsieve==2.6
      - sse-starlette==2.2.0
      - stack-data==0.6.3
      - starlette==0.41.3
      - sympy==1.13.1
      - termcolor==2.5.0
      - terminado==0.18.1
      - tiktoken==0.8.0
      - tinycss2==1.4.0
      - tokenizers==0.20.3
      - tomlkit==0.12.0
      - torch==2.5.1
      - tornado==6.4.2
      - tqdm==4.67.1
      - traitlets==5.14.3
      - transformers==4.46.1
      - triton==3.1.0
      - trl==0.9.6
      - typer==0.15.1
      - types-python-dateutil==2.9.0.20241206
      - typing-extensions==4.12.2
      - tyro==0.8.14
      - tzdata==2024.2
      - uri-template==1.3.0
      - urllib3==2.3.0
      - uvicorn==0.34.0
      - wcwidth==0.2.13
      - webcolors==24.11.1
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - websockets==12.0
      - xxhash==3.5.0
      - yarl==1.18.3
prefix: /workspace/mimiconda3/envs/topo_qwen
