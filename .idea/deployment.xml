<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="yantao@202.199.6.198:22 password (5)" remoteFilesAllowedToDisappearOnAutoupload="false">
    <serverData>
      <paths name="yantao@202.199.6.198:22 password (2)">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/code" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="yantao@202.199.6.198:22 password (3)">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/code" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="yantao@202.199.6.198:22 password (4)">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/code" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="yantao@202.199.6.198:22 password (5)">
        <serverdata>
          <mappings>
            <mapping deploy="/tmp/pycharm_project_351" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>